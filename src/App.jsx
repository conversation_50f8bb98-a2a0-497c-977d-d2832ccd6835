import "./App.css";
import React, { Suspense } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import "./i18n";
import Layout from "./Layout";
import LazySection from "./components/LazySection";
import ResourcePreloader from "./components/ResourcePreloader";

// Immediate load components (critical for first paint)
import Main_section from "./components/main_herosection/Main_section";

// Lazy load heavy components for home page with dynamic imports
const ManagementFeatures = React.lazy(() =>
  import("./components/ManagementFeatures/ManagementFeatures").then(
    (module) => ({
      default: module.default,
    })
  )
);
const SocialMediaIcons = React.lazy(() =>
  import("./components/social_media/SocialMediaIcons").then((module) => ({
    default: module.default,
  }))
);
const InfoCard = React.lazy(() =>
  import("./components/InfoCard/InfoCard").then((module) => ({
    default: module.default,
  }))
);
const About_Us = React.lazy(() =>
  import("./components/AboutUs/index").then((module) => ({
    default: module.default,
  }))
);
const Thoughts = React.lazy(() =>
  import("./components/Thoughts/Thoughts").then((module) => ({
    default: module.default,
  }))
);
const Testimonials = React.lazy(() =>
  import("./components/Testimonials/Testimonials").then((module) => ({
    default: module.default,
  }))
);
const TextReveal = React.lazy(() =>
  import("./components/TextReveal/TextReveal").then((module) => ({
    default: module.default,
  }))
);
const FAQ = React.lazy(() =>
  import("./components/FAQ/FAQ").then((module) => ({
    default: module.default,
  }))
);

// Lazy load other route components
import About_Us_Hero from "./components/AboutUs/AboutUsHero";
const Delete = React.lazy(() => import("./components/Delete"));
const Terms = React.lazy(() => import("./components/Terms/Terms"));
const ContactUs = React.lazy(() => import("./components/Contactus/ContactUs"));
const Policy = React.lazy(() => import("./components/Privacy_Policy/Policy"));
const RedirectingLinks = React.lazy(() =>
  import("./components/RedirectingLinks/RedirectingLinks")
);
const ShareProfile = React.lazy(() =>
  import("./components/ShareProfile/ShareProfile")
);
const PDFViewer = React.lazy(() => import("./components/vishal/vishal"));
const MilanCard = React.lazy(() => import("./components/milan/milan"));
const SharePost = React.lazy(() =>
  import("./components/ShareProfile/SharePost")
);
const Features = React.lazy(() => import("./components/Features/Features"));
const Blogs = React.lazy(() => import("./components/Blogs/index"));
const SingleBlogView = React.lazy(() =>
  import("./components/Blogs/SingleBlogView")
);

// Import loading components
import {
  LoadingSpinnerWithText,
  MinimalSkeleton,
  FeatureCardsSkeleton,
  SocialMediaSkeleton,
  TestimonialsSkeleton,
  InfoCardSkeleton,
  AboutUsSkeleton,
  ThoughtsSkeleton,
  FAQSkeleton,
  TextRevealSkeleton,
  BlogsSkeleton,
} from "./components/LoadingComponents";

// Import performance monitor for development

function App() {
  return (
    <Router>
      <ResourcePreloader />
      <Routes>
        {/* Routes inside Layout (Navbar and Footer included) */}
        <Route element={<Layout />}>
          <Route
            path="/"
            element={
              <>
                {/* Load hero section immediately for fast first paint */}
                <Main_section />

                {/* Lazy load remaining sections with intersection observer */}
                <LazySection
                  fallback={<FeatureCardsSkeleton />}
                  rootMargin="200px"
                  minHeight="400px"
                >
                  <Suspense fallback={<FeatureCardsSkeleton />}>
                    <ManagementFeatures />
                  </Suspense>
                </LazySection>

                <LazySection
                  fallback={<SocialMediaSkeleton />}
                  rootMargin="150px"
                  minHeight="300px"
                >
                  <Suspense fallback={<SocialMediaSkeleton />}>
                    <SocialMediaIcons />
                  </Suspense>
                </LazySection>

                <LazySection
                  fallback={<InfoCardSkeleton />}
                  rootMargin="150px"
                  minHeight="250px"
                >
                  <Suspense fallback={<InfoCardSkeleton />}>
                    <InfoCard />
                  </Suspense>
                </LazySection>

                <LazySection
                  fallback={<MinimalSkeleton height="500px" />}
                  rootMargin="150px"
                  minHeight="500px"
                >
                  <Suspense fallback={<MinimalSkeleton height="500px" />}>
                    <About_Us_Hero />
                  </Suspense>
                </LazySection>

                <LazySection
                  fallback={<ThoughtsSkeleton />}
                  rootMargin="150px"
                  minHeight="400px"
                >
                  <Suspense fallback={<ThoughtsSkeleton />}>
                    <Thoughts />
                  </Suspense>
                </LazySection>

                <LazySection
                  fallback={<TestimonialsSkeleton />}
                  rootMargin="150px"
                  minHeight="400px"
                >
                  <Suspense fallback={<TestimonialsSkeleton />}>
                    <Testimonials />
                  </Suspense>
                </LazySection>

                <LazySection
                  fallback={<TextRevealSkeleton />}
                  rootMargin="150px"
                  minHeight="300px"
                >
                  <Suspense fallback={<TextRevealSkeleton />}>
                    <TextReveal />
                  </Suspense>
                </LazySection>

                <LazySection
                  fallback={<FAQSkeleton />}
                  rootMargin="150px"
                  minHeight="400px"
                >
                  <Suspense fallback={<FAQSkeleton />}>
                    <FAQ />
                  </Suspense>
                </LazySection>
              </>
            }
          />

          <Route
            path="/aboutus"
            element={
              <Suspense fallback={<AboutUsSkeleton />}>
                <About_Us />
              </Suspense>
            }
          />
          <Route
            path="/solutions"
            element={
              <Suspense
                fallback={
                  <LoadingSpinnerWithText text="Loading Solutions..." />
                }
              >
                <Features />
              </Suspense>
            }
          />
          <Route
            path="/blogs"
            element={
              <Suspense fallback={<BlogsSkeleton />}>
                <Blogs />
              </Suspense>
            }
          />
          <Route
            path="/blog/:slug"
            element={
              <Suspense
                fallback={<LoadingSpinnerWithText text="Loading Article..." />}
              >
                <SingleBlogView />
              </Suspense>
            }
          />
          <Route
            path="/testimonials"
            element={
              <Suspense fallback={<TestimonialsSkeleton />}>
                <Testimonials />
              </Suspense>
            }
          />
          <Route
            path="/delete"
            element={
              <Suspense fallback={<LoadingSpinnerWithText text="Loading..." />}>
                <Delete />
              </Suspense>
            }
          />
          <Route
            path="/terms"
            element={
              <Suspense
                fallback={<LoadingSpinnerWithText text="Loading Terms..." />}
              >
                <Terms />
              </Suspense>
            }
          />
          <Route
            path="/privacy-policy"
            element={
              <Suspense
                fallback={
                  <LoadingSpinnerWithText text="Loading Privacy Policy..." />
                }
              >
                <Policy />
              </Suspense>
            }
          />
          <Route
            path="/contact-us"
            element={
              <Suspense
                fallback={<LoadingSpinnerWithText text="Loading Contact..." />}
              >
                <ContactUs />
              </Suspense>
            }
          />
        </Route>

        <Route
          path="/app"
          element={
            <Suspense
              fallback={<LoadingSpinnerWithText text="Redirecting..." />}
            >
              <RedirectingLinks />
            </Suspense>
          }
        />
        <Route
          path="/share-profile"
          element={
            <Suspense
              fallback={<LoadingSpinnerWithText text="Loading Profile..." />}
            >
              <ShareProfile />
            </Suspense>
          }
        />
        <Route
          path="/employees/FR-00103_vishal-kathiriya"
          element={
            <Suspense
              fallback={
                <LoadingSpinnerWithText text="Loading Employee Profile..." />
              }
            >
              <PDFViewer />
            </Suspense>
          }
        />
        <Route
          path="/employees/FR-3489720_milan-katrodiya"
          element={
            <Suspense
              fallback={
                <LoadingSpinnerWithText text="Loading Employee Profile..." />
              }
            >
              <MilanCard />
            </Suspense>
          }
        />
        <Route
          path="/get-post"
          element={
            <Suspense
              fallback={<LoadingSpinnerWithText text="Loading Post..." />}
            >
              <SharePost />
            </Suspense>
          }
        />
      </Routes>
    </Router>
  );
}

export default App;
