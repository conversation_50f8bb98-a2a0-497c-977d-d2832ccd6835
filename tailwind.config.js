/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: "class",
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      screens: {
        xs: "374px",
      },
      colors: {
        darkBlack: "#151515",
        darkGrayCard: "#202020",
        darkGrayFaq: "#1e1e1e",
        brown: "#000000"
      },
      animation: {
        marquee: "marquee 15s linear infinite",
        "spin-slow": "spin 20s linear infinite",
      },
      keyframes: {
        marquee: {
          "0%": { transform: "translateX(0)" },
          "100%": { transform: "translateX(-10%)" },
        },
      },
    },
    fontFamily: {
      sans: ["Figtree", "ui-sans-serif", "system-ui", "sans-serif"],
      figtree: ["Figtree", "sans-serif"],
    },
  },
  plugins: [],
};
